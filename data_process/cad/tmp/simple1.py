import ezdxf
import json
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple
import numpy as np


class DXFTableParser:
    """专注于多图纸划分和表格解析的DXF解析器"""

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.all_entities = []

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            return True
        except Exception as e:
            print(f"加载DXF文件失败: {e}")
            return False

    def detect_drawing_sheets(self) -> List[Dict]:
        """检测多个图纸区域"""
        # 收集所有文本实体的坐标
        text_coords = []

        for entity in self.doc.modelspace():
            if entity.dxftype() in ['TEXT', 'MTEXT']:
                insert_point = entity.dxf.insert
                text_coords.append((insert_point.x, insert_point.y))

        if not text_coords:
            return [{"图纸名称": "主图纸", "X范围": [0, 0], "Y范围": [0, 0]}]

        # 计算坐标分布
        x_coords = [coord[0] for coord in text_coords]
        y_coords = [coord[1] for coord in text_coords]

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        x_range = x_max - x_min

        # 简单的双图纸检测：如果X范围很大，尝试分割
        if x_range > 100000:  # 阈值可调整
            x_mid = (x_min + x_max) / 2
            left_texts = [coord for coord in text_coords if coord[0] < x_mid]
            right_texts = [coord for coord in text_coords if coord[0] >= x_mid]

            if len(left_texts) > 50 and len(right_texts) > 50:
                left_x = [coord[0] for coord in left_texts]
                left_y = [coord[1] for coord in left_texts]
                right_x = [coord[0] for coord in right_texts]
                right_y = [coord[1] for coord in right_texts]

                return [
                    {
                        "图纸名称": "图纸1",
                        "X范围": [min(left_x), max(left_x)],
                        "Y范围": [min(left_y), max(left_y)]
                    },
                    {
                        "图纸名称": "图纸2",
                        "X范围": [min(right_x), max(right_x)],
                        "Y范围": [min(right_y), max(right_y)]
                    }
                ]

        return [{
            "图纸名称": "主图纸",
            "X范围": [x_min, x_max],
            "Y范围": [y_min, y_max]
        }]

    def extract_table_data(self, sheet_bounds: Dict) -> List[Dict]:
        """提取表格数据"""
        # 获取指定图层的实体
        frame_entities = []  # TK_BTXT 图层的线框
        text_entities = []  # TK_BTWZ 图层的文字
        chart_entities = []  # SYST 图层的图表数据

        x_min, x_max = sheet_bounds["X范围"]
        y_min, y_max = sheet_bounds["Y范围"]

        for entity in self.doc.modelspace():
            # 检查实体是否在当前图纸范围内
            entity_pos = self._get_entity_position(entity)
            if not entity_pos:
                continue

            x, y = entity_pos
            if not (x_min <= x <= x_max and y_min <= y <= y_max):
                continue

            layer = entity.dxf.layer.upper()

            # 分类收集实体
            if layer == 'TK_BTXT' and entity.dxftype() in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                frame_entities.append(entity)
            elif layer == 'TK_BTWZ' and entity.dxftype() in ['TEXT', 'MTEXT']:
                text_entities.append(entity)
            elif layer == 'SYST':
                chart_entities.append(entity)

        tables = []

        # 解析表格（文字+线框）
        if frame_entities and text_entities:
            table_data = self._parse_table_from_frame_and_text(frame_entities, text_entities)
            if table_data:
                tables.append({
                    "表格类型": "框线表格",
                    "数据来源": "TK_BTXT(线框) + TK_BTWZ(文字)",
                    "表格数据": table_data
                })

        # 解析图表数据
        if chart_entities:
            chart_data = self._parse_chart_data(chart_entities)
            if chart_data:
                tables.append({
                    "表格类型": "图表数据",
                    "数据来源": "SYST图层",
                    "表格数据": chart_data
                })

        return tables

    def _get_entity_position(self, entity) -> Optional[Tuple[float, float]]:
        """获取实体位置"""
        try:
            if hasattr(entity.dxf, 'insert'):
                return (entity.dxf.insert.x, entity.dxf.insert.y)
            elif hasattr(entity.dxf, 'start'):
                return (entity.dxf.start.x, entity.dxf.start.y)
            elif hasattr(entity.dxf, 'center'):
                return (entity.dxf.center.x, entity.dxf.center.y)
        except:
            pass
        return None

    def _parse_table_from_frame_and_text(self, frame_entities: List, text_entities: List) -> Dict:
        """从线框和文字解析表格"""
        # 第1步：从线框实体中提取网格线
        x_coords = set()
        y_coords = set()

        for entity in frame_entities:
            if entity.dxftype() == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end

                # 检查是否为水平或垂直线（允许小误差）
                if abs(start.x - end.x) < 1.0:  # 垂直线
                    x_coords.add(start.x)
                elif abs(start.y - end.y) < 1.0:  # 水平线
                    y_coords.add(start.y)

            elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
                # 处理多段线
                vertices = list(entity.vertices)
                for i in range(len(vertices)):
                    start = vertices[i]
                    end = vertices[(i + 1) % len(vertices)]

                    if abs(start[0] - end[0]) < 1.0:  # 垂直线
                        x_coords.add(start[0])
                    elif abs(start[1] - end[1]) < 1.0:  # 水平线
                        y_coords.add(start[1])

        if not x_coords or not y_coords:
            return {}

        # 第2步：生成单元格网格
        sorted_x = sorted(list(x_coords))
        sorted_y = sorted(list(y_coords), reverse=True)  # Y坐标从大到小排序

        cells = []
        for i in range(len(sorted_x) - 1):
            for j in range(len(sorted_y) - 1):
                cell = {
                    "边界": {
                        "x_min": sorted_x[i],
                        "x_max": sorted_x[i + 1],
                        "y_min": sorted_y[j + 1],
                        "y_max": sorted_y[j]
                    },
                    "行": j,
                    "列": i,
                    "内容": []
                }
                cells.append(cell)

        # 第3步：将文字分配到单元格
        for text_entity in text_entities:
            text_pos = text_entity.dxf.insert
            text_content = text_entity.dxf.text if hasattr(text_entity.dxf, 'text') else text_entity.plain_text()

            for cell in cells:
                bounds = cell["边界"]
                if (bounds["x_min"] < text_pos.x < bounds["x_max"] and
                        bounds["y_min"] < text_pos.y < bounds["y_max"]):
                    cell["内容"].append(text_content.strip())
                    break

        # 第4步：构建表格结构
        max_row = max(cell["行"] for cell in cells) + 1
        max_col = max(cell["列"] for cell in cells) + 1

        table_matrix = [[[] for _ in range(max_col)] for _ in range(max_row)]

        for cell in cells:
            row, col = cell["行"], cell["列"]
            table_matrix[row][col] = cell["内容"]

        # 第5步：转换为键值对格式
        structured_data = {}
        table_rows = []

        for row_idx, row in enumerate(table_matrix):
            row_data = []
            for col_idx, cell_content in enumerate(row):
                cell_text = " ".join(cell_content) if cell_content else ""
                row_data.append(cell_text)

            if any(row_data):  # 只保留非空行
                table_rows.append({
                    "行号": row_idx,
                    "数据": row_data
                })

        # 尝试识别键值对关系
        key_value_pairs = {}
        for row in table_rows:
            data = row["数据"]
            for i in range(0, len(data) - 1, 2):  # 每两列作为一对
                key = data[i].strip()
                value = data[i + 1].strip()
                if key and value:
                    key_value_pairs[key] = value

        return {
            "表格矩阵": table_rows,
            "键值对": key_value_pairs,
            "表格尺寸": {"行数": max_row, "列数": max_col}
        }

    def _parse_chart_data(self, chart_entities: List) -> Dict:
        """解析SYST图层的图表数据"""
        chart_data = {
            "文本数据": [],
            "几何数据": [],
            "统计": {"文本数量": 0, "几何数量": 0}
        }

        for entity in chart_entities:
            if entity.dxftype() in ['TEXT', 'MTEXT']:
                text_content = entity.dxf.text if hasattr(entity.dxf, 'text') else entity.plain_text()
                position = (entity.dxf.insert.x, entity.dxf.insert.y)

                chart_data["文本数据"].append({
                    "内容": text_content.strip(),
                    "位置": position,
                    "图层": entity.dxf.layer
                })
                chart_data["统计"]["文本数量"] += 1

            else:
                # 几何实体
                entity_info = {
                    "类型": entity.dxftype(),
                    "图层": entity.dxf.layer
                }

                # 根据实体类型添加具体信息
                if entity.dxftype() == 'LINE':
                    entity_info["起点"] = (entity.dxf.start.x, entity.dxf.start.y)
                    entity_info["终点"] = (entity.dxf.end.x, entity.dxf.end.y)
                elif entity.dxftype() == 'CIRCLE':
                    entity_info["圆心"] = (entity.dxf.center.x, entity.dxf.center.y)
                    entity_info["半径"] = entity.dxf.radius

                chart_data["几何数据"].append(entity_info)
                chart_data["统计"]["几何数量"] += 1

        # 按行列组织文本数据
        if chart_data["文本数据"]:
            organized_data = self._organize_chart_text_by_grid(chart_data["文本数据"])
            chart_data["网格化数据"] = organized_data

        return chart_data

    def _organize_chart_text_by_grid(self, text_data: List[Dict]) -> Dict:
        """将图表文本按网格组织"""
        if not text_data:
            return {}

        # 按Y坐标分组（行）
        y_groups = defaultdict(list)
        for item in text_data:
            y_coord = round(item["位置"][1] / 100) * 100  # 按100单位分组
            y_groups[y_coord].append(item)

        # 对每行按X坐标排序
        grid_data = []
        for y_coord in sorted(y_groups.keys(), reverse=True):
            row_items = sorted(y_groups[y_coord], key=lambda x: x["位置"][0])
            row_data = [item["内容"] for item in row_items]
            grid_data.append({
                "Y坐标": y_coord,
                "行数据": row_data
            })

        return {
            "行数": len(grid_data),
            "数据": grid_data
        }

    def generate_output(self) -> Dict:
        """生成完整输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 检测图纸
        drawing_sheets = self.detect_drawing_sheets()

        # 为每个图纸解析表格
        for sheet in drawing_sheets:
            sheet["主图"] = {"描述": "主要绘图内容"}
            sheet["图表"] = self.extract_table_data(sheet)

        return {
            "文件信息": {
                "文件名": os.path.basename(self.dxf_path),
                "DXF版本": self.doc.dxfversion if self.doc else "未知"
            },
            "图纸结构": {
                "图纸数量": len(drawing_sheets),
                "图纸列表": drawing_sheets
            }
        }


def process_dxf_files(input_path: str, output_dir: str = None) -> Dict:
    """处理DXF文件（支持单文件和批量）"""
    input_path = Path(input_path)

    if not input_path.exists():
        return {"错误": f"路径不存在: {input_path}"}

    # 获取DXF文件列表
    if input_path.is_file():
        dxf_files = [input_path] if input_path.suffix.lower() == '.dxf' else []
    else:
        dxf_files = list(input_path.glob('*.dxf')) + list(input_path.glob('*.DXF'))

    if not dxf_files:
        return {"错误": "未找到DXF文件"}

    # 设置输出目录
    if not output_dir:
        output_dir = input_path.parent / f"{input_path.stem}_parsed" if input_path.is_file() else input_path / "parsed_output"
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(parents=True, exist_ok=True)

    results = {"成功": 0, "失败": 0, "总计": len(dxf_files), "失败文件": []}

    # 处理每个文件
    for dxf_file in dxf_files:
        try:
            parser = DXFTableParser(str(dxf_file))
            result = parser.generate_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                continue

            # 保存结果
            output_file = output_dir / f"{dxf_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            results["成功"] += 1
            print(f"✓ 成功处理: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            print(f"✗ 处理失败: {dxf_file.name} - {e}")

    print(f"\n处理完成: 成功 {results['成功']}/{results['总计']}")
    return results


if __name__ == '__main__':
    # 使用示例
    input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/'  # 请修改为实际路径
    output_dir = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_v3'  # 请修改为实际路径

    results = process_dxf_files(input_path, output_dir)