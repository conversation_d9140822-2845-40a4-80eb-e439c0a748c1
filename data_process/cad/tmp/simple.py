import ezdxf
import json
import os
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional, Tuple
from tqdm import tqdm
import math


def safe_coordinate_conversion(coord) -> List[float]:
    """安全地将坐标对象转换为列表"""
    if coord is None:
        return []

    try:
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]

        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)
            return [float(coord.x), float(coord.y), float(z)]

        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]

        return [float(coord)]

    except Exception as e:
        print(f"坐标转换警告: {e}")
        return []


def safe_json_conversion(obj) -> Any:
    """安全地将对象转换为JSON可序列化的格式"""
    if obj is None:
        return None

    try:
        if isinstance(obj, (str, int, float, bool)):
            return obj

        if isinstance(obj, (list, tuple)):
            return [safe_json_conversion(item) for item in obj]

        if isinstance(obj, dict):
            return {key: safe_json_conversion(value) for key, value in obj.items()}

        if hasattr(obj, 'x') and hasattr(obj, 'y'):
            z = getattr(obj, 'z', 0.0)
            return [float(obj.x), float(obj.y), float(z)]

        if hasattr(obj, '__iter__') and not isinstance(obj, str):
            return [safe_json_conversion(item) for item in obj]

        return str(obj)

    except Exception as e:
        print(f"JSON转换警告: {e}")
        return str(obj)


class TableReconstructor:
    """表格重建器 - 从文字图层和线框图层重建表格结构"""

    def __init__(self, tolerance: float = 5.0):
        """
        初始化表格重建器

        Args:
            tolerance: 坐标比较的容差值
        """
        self.tolerance = tolerance

    def extract_lines_and_texts(self, entities: List[Dict], text_layer: str, frame_layer: str) -> Tuple[
        List[Dict], List[Dict]]:
        """
        从实体列表中提取线框和文字

        Args:
            entities: 实体列表
            text_layer: 文字图层名称 (如 tk_btwz)
            frame_layer: 线框图层名称 (如 tk_btxt)

        Returns:
            (lines, texts): 线框列表和文字列表
        """
        lines = []
        texts = []

        for entity in entities:
            layer = entity.get("所在图层", "")
            entity_type = entity.get("实体类型", "")

            # 提取线框实体 (LINE, LWPOLYLINE等)
            if layer == frame_layer and entity_type in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                if entity.get("几何信息"):
                    lines.append(entity)

            # 提取文字实体 (TEXT, MTEXT)
            elif layer == text_layer and entity_type in ['TEXT', 'MTEXT']:
                if entity.get("文本信息"):
                    texts.append(entity)

        print(f"提取到 {len(lines)} 个线框实体，{len(texts)} 个文字实体")
        return lines, texts

    def extract_grid_coordinates(self, lines: List[Dict]) -> Tuple[List[float], List[float]]:
        """
        从线框中提取网格坐标

        Args:
            lines: 线框实体列表

        Returns:
            (x_coords, y_coords): 排序后的X坐标和Y坐标列表
        """
        x_coords = set()
        y_coords = set()

        for line in lines:
            geo_info = line.get("几何信息", {})

            if line["实体类型"] == "LINE":
                # 处理直线
                start = geo_info.get("起点坐标", [])
                end = geo_info.get("终点坐标", [])

                if len(start) >= 2 and len(end) >= 2:
                    # 判断是否为水平线或垂直线
                    if abs(start[0] - end[0]) < self.tolerance:  # 垂直线
                        x_coords.add(start[0])
                    elif abs(start[1] - end[1]) < self.tolerance:  # 水平线
                        y_coords.add(start[1])

            elif line["实体类型"] in ["LWPOLYLINE", "POLYLINE"]:
                # 处理多段线
                vertices = geo_info.get("顶点坐标", [])
                if isinstance(vertices, list) and len(vertices) > 1:
                    for i in range(len(vertices) - 1):
                        # 解析顶点坐标字符串 "(x, y)" 或直接使用坐标列表
                        start_coords = self._parse_vertex_coord(vertices[i])
                        end_coords = self._parse_vertex_coord(vertices[i + 1])

                        if start_coords and end_coords:
                            # 判断线段方向
                            if abs(start_coords[0] - end_coords[0]) < self.tolerance:  # 垂直线
                                x_coords.add(start_coords[0])
                            elif abs(start_coords[1] - end_coords[1]) < self.tolerance:  # 水平线
                                y_coords.add(start_coords[1])

        # 排序并返回
        sorted_x = sorted(list(x_coords))
        sorted_y = sorted(list(y_coords), reverse=True)  # Y坐标从上到下

        print(f"提取到网格坐标: X轴 {len(sorted_x)} 个点, Y轴 {len(sorted_y)} 个点")
        return sorted_x, sorted_y

    def _parse_vertex_coord(self, vertex) -> Optional[List[float]]:
        """解析顶点坐标"""
        if isinstance(vertex, list) and len(vertex) >= 2:
            return [float(vertex[0]), float(vertex[1])]
        elif isinstance(vertex, str):
            # 解析 "(x, y)" 格式的字符串
            try:
                vertex = vertex.strip("()")
                coords = vertex.split(",")
                if len(coords) >= 2:
                    return [float(coords[0].strip()), float(coords[1].strip())]
            except:
                pass
        return None

    def generate_cells(self, x_coords: List[float], y_coords: List[float]) -> List[Dict]:
        """
        根据网格坐标生成单元格

        Args:
            x_coords: X坐标列表
            y_coords: Y坐标列表 (从上到下)

        Returns:
            单元格列表
        """
        cells = []

        for row in range(len(y_coords) - 1):
            for col in range(len(x_coords) - 1):
                cell = {
                    "行号": row,
                    "列号": col,
                    "边界框": {
                        "左": x_coords[col],
                        "右": x_coords[col + 1],
                        "上": y_coords[row],
                        "下": y_coords[row + 1]
                    },
                    "文本内容": [],
                    "原始文本实体": []
                }
                cells.append(cell)

        print(f"生成了 {len(cells)} 个单元格")
        return cells

    def assign_texts_to_cells(self, cells: List[Dict], texts: List[Dict]) -> List[Dict]:
        """
        将文字分配到对应的单元格中

        Args:
            cells: 单元格列表
            texts: 文字实体列表

        Returns:
            填充了文本内容的单元格列表
        """
        assigned_count = 0

        for text in texts:
            text_info = text.get("文本信息", {})
            insert_point = text_info.get("插入点坐标", [])
            text_content = text_info.get("文本内容", "").strip()

            if len(insert_point) >= 2 and text_content:
                text_x, text_y = insert_point[0], insert_point[1]

                # 查找包含此文本的单元格
                for cell in cells:
                    bbox = cell["边界框"]
                    if (bbox["左"] <= text_x <= bbox["右"] and
                            bbox["下"] <= text_y <= bbox["上"]):
                        cell["文本内容"].append(text_content)
                        cell["原始文本实体"].append(text)
                        assigned_count += 1
                        break

        print(f"成功分配了 {assigned_count} 个文本到单元格中")
        return cells

    def cells_to_table_structure(self, cells: List[Dict]) -> Dict:
        """
        将单元格转换为表格结构

        Args:
            cells: 单元格列表

        Returns:
            表格结构字典
        """
        if not cells:
            return {"表格数据": [], "行数": 0, "列数": 0}

        # 计算表格维度
        max_row = max(cell["行号"] for cell in cells) + 1
        max_col = max(cell["列号"] for cell in cells) + 1

        # 创建二维表格数组
        table_data = []
        for row in range(max_row):
            row_data = []
            for col in range(max_col):
                # 查找对应的单元格
                cell_content = ""
                for cell in cells:
                    if cell["行号"] == row and cell["列号"] == col:
                        if cell["文本内容"]:
                            cell_content = " ".join(cell["文本内容"])
                        break
                row_data.append(cell_content)
            table_data.append(row_data)

        return {
            "表格数据": table_data,
            "行数": max_row,
            "列数": max_col,
            "单元格详情": cells
        }

    def reconstruct_table(self, entities: List[Dict], text_layer: str = "tk_btwz",
                          frame_layer: str = "tk_btxt") -> Dict:
        """
        重建表格的完整流程

        Args:
            entities: 实体列表
            text_layer: 文字图层名称
            frame_layer: 线框图层名称

        Returns:
            重建的表格结构
        """
        print(f"开始重建表格: 文字图层={text_layer}, 线框图层={frame_layer}")

        # 1. 提取线框和文字
        lines, texts = self.extract_lines_and_texts(entities, text_layer, frame_layer)

        if not lines or not texts:
            print("警告: 未找到足够的线框或文字实体")
            return {"错误": "未找到足够的线框或文字实体"}

        # 2. 提取网格坐标
        x_coords, y_coords = self.extract_grid_coordinates(lines)

        if len(x_coords) < 2 or len(y_coords) < 2:
            print("警告: 网格坐标不足以构成表格")
            return {"错误": "网格坐标不足以构成表格"}

        # 3. 生成单元格
        cells = self.generate_cells(x_coords, y_coords)

        # 4. 分配文字到单元格
        cells = self.assign_texts_to_cells(cells, texts)

        # 5. 转换为表格结构
        table_structure = self.cells_to_table_structure(cells)

        print("表格重建完成")
        return table_structure


class DXFMultiSheetParser:
    """DXF多图纸解析器 - 专注于多图纸划分和表格解析"""

    def __init__(self, dxf_path: str):
        """
        初始化解析器

        Args:
            dxf_path: DXF文件路径
        """
        self.dxf_path = dxf_path
        self.doc = None
        self.all_entities = []
        self.table_reconstructor = TableReconstructor()

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            print(f"成功加载DXF文件: {self.dxf_path}")
            return True
        except Exception as e:
            print(f"错误: 无法读取DXF文件: {e}")
            return False

    def extract_entity_info(self, entity) -> Dict:
        """提取实体基本信息"""
        entity_type = entity.dxftype()

        # 基础信息
        entity_info = {
            "实体类型": entity_type,
            "所在图层": getattr(entity.dxf, 'layer', ''),
            "颜色索引": getattr(entity.dxf, 'color', 0)
        }

        # 根据实体类型提取详细信息
        if entity_type in ['TEXT', 'MTEXT']:
            entity_info["文本信息"] = self._extract_text_info(entity)

        elif entity_type == 'LINE':
            entity_info["几何信息"] = {
                "起点坐标": safe_coordinate_conversion(entity.dxf.start),
                "终点坐标": safe_coordinate_conversion(entity.dxf.end)
            }

        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            vertices = []
            try:
                for vertex in entity:
                    if hasattr(vertex, 'dxf'):
                        vertices.append(safe_coordinate_conversion(vertex.dxf.location))
                    else:
                        vertices.append(safe_coordinate_conversion(vertex))
            except:
                pass

            entity_info["几何信息"] = {
                "顶点坐标": vertices,
                "是否闭合": getattr(entity, 'is_closed', False)
            }

        elif entity_type == 'INSERT':
            entity_info["块信息"] = self._extract_block_info(entity)

        return entity_info

    def _extract_text_info(self, entity) -> Dict:
        """提取文本信息"""
        text_info = {}
        entity_type = entity.dxftype()

        try:
            if entity_type == 'TEXT':
                text_info["文本内容"] = entity.dxf.text.strip()
                text_info["插入点坐标"] = safe_coordinate_conversion(entity.dxf.insert)
                text_info["文本高度"] = getattr(entity.dxf, 'height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

            elif entity_type == 'MTEXT':
                text_info["文本内容"] = entity.plain_text().strip()
                text_info["插入点坐标"] = safe_coordinate_conversion(entity.dxf.insert)
                text_info["字符高度"] = getattr(entity.dxf, 'char_height', 0)
                text_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

        except Exception as e:
            text_info["提取错误"] = str(e)

        return text_info

    def _extract_block_info(self, entity) -> Dict:
        """提取块信息"""
        block_info = {}

        try:
            block_info["块名称"] = getattr(entity.dxf, 'name', '')
            block_info["插入点坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            block_info["缩放比例"] = {
                "X": getattr(entity.dxf, 'xscale', 1.0),
                "Y": getattr(entity.dxf, 'yscale', 1.0),
                "Z": getattr(entity.dxf, 'zscale', 1.0)
            }
            block_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

            # 提取块属性
            attributes = []
            if hasattr(entity, 'attribs'):
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attr_info = {
                            "标签": getattr(attrib.dxf, 'tag', ''),
                            "值": attrib.dxf.text.strip(),
                            "位置": safe_coordinate_conversion(attrib.dxf.insert)
                        }
                        attributes.append(attr_info)

            block_info["属性列表"] = attributes

        except Exception as e:
            block_info["提取错误"] = str(e)

        return block_info

    def collect_all_entities(self):
        """收集所有实体"""
        self.all_entities = []

        # 模型空间
        for entity in self.doc.modelspace():
            entity_info = self.extract_entity_info(entity)
            entity_info["空间类型"] = "模型空间"
            self.all_entities.append(entity_info)

        # 图纸空间
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    entity_info = self.extract_entity_info(entity)
                    entity_info["空间类型"] = f"图纸空间-{layout.name}"
                    self.all_entities.append(entity_info)

        # 块定义
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    entity_info = self.extract_entity_info(entity)
                    entity_info["空间类型"] = f"块定义-{block.name}"
                    self.all_entities.append(entity_info)

        print(f"收集到 {len(self.all_entities)} 个实体")

    def detect_drawing_sheets(self) -> List[Dict]:
        """
        检测多个图纸区域
        基于文本和块的坐标分布进行图纸划分
        """
        print("开始检测图纸区域...")

        # 收集所有有坐标的实体
        entities_with_coords = []
        for entity in self.all_entities:
            coords = None

            # 从文本信息中获取坐标
            if entity.get("文本信息") and entity["文本信息"].get("插入点坐标"):
                coords = entity["文本信息"]["插入点坐标"]
            # 从块信息中获取坐标
            elif entity.get("块信息") and entity["块信息"].get("插入点坐标"):
                coords = entity["块信息"]["插入点坐标"]
            # 从几何信息中获取坐标
            elif entity.get("几何信息"):
                if entity["几何信息"].get("起点坐标"):
                    coords = entity["几何信息"]["起点坐标"]

            if coords and len(coords) >= 2:
                entities_with_coords.append({
                    "坐标": coords,
                    "实体": entity
                })

        if not entities_with_coords:
            print("警告: 未找到有坐标的实体")
            return []

        # 计算坐标范围
        x_coords = [e["坐标"][0] for e in entities_with_coords]
        y_coords = [e["坐标"][1] for e in entities_with_coords]

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        x_range = x_max - x_min

        print(f"坐标范围: X({x_min:.2f} ~ {x_max:.2f}), Y({y_min:.2f} ~ {y_max:.2f})")

        # 判断是否为多图纸
        if x_range < 100000:  # 单个图纸
            print("检测到单个图纸")
            return [{
                "图纸名称": "主图纸",
                "图纸编号": 1,
                "坐标范围": {
                    "X范围": [x_min, x_max],
                    "Y范围": [y_min, y_max]
                },
                "实体数量": len(entities_with_coords)
            }]

        # 多图纸检测 - 使用简单的X坐标中点分割
        x_mid = (x_min + x_max) / 2
        left_entities = [e for e in entities_with_coords if e["坐标"][0] < x_mid]
        right_entities = [e for e in entities_with_coords if e["坐标"][0] >= x_mid]

        sheets = []

        # 检查左侧图纸
        if len(left_entities) > 50:  # 实体数量阈值
            left_x = [e["坐标"][0] for e in left_entities]
            left_y = [e["坐标"][1] for e in left_entities]
            sheets.append({
                "图纸名称": "图纸1",
                "图纸编号": 1,
                "坐标范围": {
                    "X范围": [min(left_x), max(left_x)],
                    "Y范围": [min(left_y), max(left_y)]
                },
                "实体数量": len(left_entities)
            })

        # 检查右侧图纸
        if len(right_entities) > 50:  # 实体数量阈值
            right_x = [e["坐标"][0] for e in right_entities]
            right_y = [e["坐标"][1] for e in right_entities]
            sheets.append({
                "图纸名称": "图纸2",
                "图纸编号": 2,
                "坐标范围": {
                    "X范围": [min(right_x), max(right_x)],
                    "Y范围": [min(right_y), max(right_y)]
                },
                "实体数量": len(right_entities)
            })

        # 如果分割后的图纸数量不足，返回单图纸
        if len(sheets) < 2:
            print("分割后图纸数量不足，按单图纸处理")
            return [{
                "图纸名称": "主图纸",
                "图纸编号": 1,
                "坐标范围": {
                    "X范围": [x_min, x_max],
                    "Y范围": [y_min, y_max]
                },
                "实体数量": len(entities_with_coords)
            }]

        print(f"检测到 {len(sheets)} 个图纸")
        return sheets

    def assign_entities_to_sheets(self, sheets: List[Dict]) -> List[Dict]:
        """将实体分配到对应的图纸"""
        print("开始分配实体到图纸...")

        # 为每个图纸初始化实体列表
        for sheet in sheets:
            sheet["实体列表"] = []
            sheet["图层统计"] = defaultdict(int)

        # 分配实体
        for entity in self.all_entities:
            coords = None

            # 获取实体坐标
            if entity.get("文本信息") and entity["文本信息"].get("插入点坐标"):
                coords = entity["文本信息"]["插入点坐标"]
            elif entity.get("块信息") and entity["块信息"].get("插入点坐标"):
                coords = entity["块信息"]["插入点坐标"]
            elif entity.get("几何信息"):
                if entity["几何信息"].get("起点坐标"):
                    coords = entity["几何信息"]["起点坐标"]

            # 分配到对应图纸
            assigned = False
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]

                for sheet in sheets:
                    x_range = sheet["坐标范围"]["X范围"]
                    y_range = sheet["坐标范围"]["Y范围"]

                    if (x_range[0] <= x <= x_range[1] and
                            y_range[0] <= y <= y_range[1]):
                        sheet["实体列表"].append(entity)
                        sheet["图层统计"][entity.get("所在图层", "0")] += 1
                        assigned = True
                        break

            # 如果未分配，放到第一个图纸
            if not assigned and sheets:
                sheets[0]["实体列表"].append(entity)
                sheets[0]["图层统计"][entity.get("所在图层", "0")] += 1

        # 转换统计字典
        for sheet in sheets:
            sheet["图层统计"] = dict(sheet["图层统计"])
            print(f"{sheet['图纸名称']}: {len(sheet['实体列表'])} 个实体")

        return sheets

    def detect_and_parse_tables(self, sheet: Dict) -> Dict:
        """
        检测并解析图纸中的表格
        重点关注 syst 图层的图表数据和 tk_btwz/tk_btxt 图层的表格
        """
        print(f"开始解析 {sheet['图纸名称']} 中的表格...")

        sheet_entities = sheet.get("实体列表", [])

        # 分析图层分布
        layer_entities = defaultdict(list)
        for entity in sheet_entities:
            layer = entity.get("所在图层", "")
            layer_entities[layer].append(entity)

        # 检测表格结构
        tables = []

        # 1. 检测 tk_btwz + tk_btxt 表格组合
        if "tk_btwz" in layer_entities and "tk_btxt" in layer_entities:
            print("检测到 tk_btwz/tk_btxt 表格组合")

            # 合并相关实体用于表格重建
            table_entities = layer_entities["tk_btwz"] + layer_entities["tk_btxt"]

            # 重建表格
            table_result = self.table_reconstructor.reconstruct_table(
                table_entities,
                text_layer="tk_btwz",
                frame_layer="tk_btxt"
            )

            if "错误" not in table_result:
                tables.append({
                    "表格类型": "框线表格",
                    "表格名称": "tk_表格",
                    "数据来源": "tk_btwz(文字) + tk_btxt(线框)",
                    "表格结构": table_result
                })

        # 2. 检测 syst 图层的图表数据
        if "syst" in layer_entities:
            print("检测到 syst 图层图表数据")

            syst_entities = layer_entities["syst"]

            # 提取 syst 图层中的文本信息，按行列排列
            syst_texts = []
            for entity in syst_entities:
                if entity.get("文本信息"):
                    text_info = entity["文本信息"]
                    if text_info.get("文本内容") and text_info.get("插入点坐标"):
                        syst_texts.append({
                            "内容": text_info["文本内容"],
                            "坐标": text_info["插入点坐标"],
                            "实体": entity
                        })

            if syst_texts:
                # 按坐标排序，构建图表结构
                chart_data = self._organize_chart_data(syst_texts)

                tables.append({
                    "表格类型": "图表数据",
                    "表格名称": "syst_图表",
                    "数据来源": "syst图层文本",
                    "表格结构": chart_data
                })

        # 3. 检测其他可能的表格结构
        other_table_layers = [layer for layer in layer_entities.keys()
                              if layer not in ["tk_btwz", "tk_btxt", "syst"]
                              and len(layer_entities[layer]) > 10]

        for layer in other_table_layers:
            print(f"检测图层 {layer} 中的潜在表格结构")

            layer_texts = []
            for entity in layer_entities[layer]:
                if entity.get("文本信息"):
                    text_info = entity["文本信息"]
                    if text_info.get("文本内容") and text_info.get("插入点坐标"):
                        layer_texts.append({
                            "内容": text_info["文本内容"],
                            "坐标": text_info["插入点坐标"],
                            "实体": entity
                        })

            if len(layer_texts) >= 4:  # 至少4个文本才考虑构成表格
                chart_data = self._organize_chart_data(layer_texts)

                if chart_data.get("行数", 0) >= 2 and chart_data.get("列数", 0) >= 2:
                    tables.append({
                        "表格类型": "文本表格",
                        "表格名称": f"{layer}_表格",
                        "数据来源": f"{layer}图层文本",
                        "表格结构": chart_data
                    })

        print(f"在 {sheet['图纸名称']} 中检测到 {len(tables)} 个表格")

        return {
            "表格列表": tables,
            "表格数量": len(tables)
        }

    def _organize_chart_data(self, texts: List[Dict]) -> Dict:
        """
        将文本按坐标组织成图表数据结构

        Args:
            texts: 包含内容和坐标的文本列表

        Returns:
            组织后的图表数据
        """
        if not texts:
            return {"表格数据": [], "行数": 0, "列数": 0}

        # 按Y坐标分组（行）
        tolerance = 50.0  # Y坐标容差
        rows = defaultdict(list)

        for text in texts:
            y_coord = text["坐标"][1]
            # 找到最接近的行
            assigned = False
            for existing_y in rows.keys():
                if abs(y_coord - existing_y) <= tolerance:
                    rows[existing_y].append(text)
                    assigned = True
                    break

            if not assigned:
                rows[y_coord] = [text]

        # 按Y坐标排序（从上到下）
        sorted_rows = sorted(rows.items(), key=lambda x: x[0], reverse=True)

        # 构建表格数据
        table_data = []
        max_cols = 0

        for y_coord, row_texts in sorted_rows:
            # 按X坐标排序（从左到右）
            row_texts.sort(key=lambda x: x["坐标"][0])

            # 提取文本内容
            row_data = [text["内容"] for text in row_texts]
            table_data.append(row_data)
            max_cols = max(max_cols, len(row_data))

        # 补齐列数
        for row in table_data:
            while len(row) < max_cols:
                row.append("")

        return {
            "表格数据": table_data,
            "行数": len(table_data),
            "列数": max_cols,
            "原始文本实体": texts
        }

    def generate_output(self) -> Dict:
        """生成完整的解析输出"""
        print("开始生成解析输出...")

        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 收集所有实体
        self.collect_all_entities()

        # 检测图纸区域
        sheets = self.detect_drawing_sheets()

        # 分配实体到图纸
        sheets = self.assign_entities_to_sheets(sheets)

        # 为每个图纸解析表格
        for sheet in sheets:
            table_info = self.detect_and_parse_tables(sheet)
            sheet["表格信息"] = table_info

            # 移除实体列表以减少输出大小（可选）
            # sheet.pop("实体列表", None)

        # 构建最终输出
        output = {
            "文件信息": {
                "文件名": os.path.basename(self.dxf_path),
                "DXF版本": self.doc.dxfversion,
                "总实体数": len(self.all_entities)
            },
            "图纸结构": {
                "图纸数量": len(sheets),
                "图纸列表": sheets
            }
        }

        print("解析输出生成完成")
        return output


def process_dxf_file(input_path: str, output_path: Optional[str] = None) -> Dict:
    """
    处理单个DXF文件

    Args:
        input_path: 输入DXF文件路径
        output_path: 输出JSON文件路径（可选）

    Returns:
        处理结果
    """
    print(f"开始处理DXF文件: {input_path}")

    # 创建解析器
    parser = DXFMultiSheetParser(input_path)

    # 执行解析
    result = parser.generate_output()

    # 保存结果
    if output_path:
        safe_result = safe_json_conversion(result)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(safe_result, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {output_path}")

    return result


def main():
    """主函数"""
    import sys

    # 使用示例
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        # 默认示例路径
        input_file = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10.dxf'  # 请修改为实际路径
        output_file = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图10_.json'  # 请修改为实际路径

    try:
        result = process_dxf_file(input_file, output_file)

        # 打印简要统计
        if "错误" not in result:
            file_info = result.get("文件信息", {})
            sheet_info = result.get("图纸结构", {})

            print(f"\n=== 处理完成 ===")
            print(f"文件: {file_info.get('文件名', 'Unknown')}")
            print(f"DXF版本: {file_info.get('DXF版本', 'Unknown')}")
            print(f"总实体数: {file_info.get('总实体数', 0)}")
            print(f"图纸数量: {sheet_info.get('图纸数量', 0)}")

            # 打印每个图纸的表格统计
            for sheet in sheet_info.get("图纸列表", []):
                table_count = sheet.get("表格信息", {}).get("表格数量", 0)
                print(f"  {sheet.get('图纸名称', '未知图纸')}: {table_count} 个表格")

        return 0

    except Exception as e:
        print(f"处理失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())